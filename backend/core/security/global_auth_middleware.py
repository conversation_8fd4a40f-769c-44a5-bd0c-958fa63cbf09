"""
全局权限验证中间件
作为装饰器权限验证的双重保障，确保所有API接口都有适当的权限保护
"""

import logging
from flask import request, jsonify, session, g
from functools import wraps
from config.security_settings import API_PERMISSIONS, PAGE_PERMISSIONS, PERMISSION_LEVELS

logger = logging.getLogger(__name__)

class GlobalAuthMiddleware:
    """全局权限验证中间件"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化中间件"""
        app.before_request(self.before_request)
        logger.info("全局权限验证中间件已初始化")
    
    def before_request(self):
        """请求前的权限验证"""
        # 获取请求路径和方法
        path = request.path
        method = request.method
        
        # 跳过静态文件和特殊路径
        if self._should_skip_auth(path):
            return None
        
        # 检查API权限
        if path.startswith('/api/'):
            return self._check_api_permission(path, method)
        
        # 检查页面权限
        return self._check_page_permission(path)
    
    def _should_skip_auth(self, path):
        """判断是否应该跳过权限验证"""
        skip_patterns = [
            '/static/',
            '/js/',
            '/css/',
            '/assets/',
            '/frontend/',
            '/favicon.ico',
            '/_debug_toolbar',  # Flask调试工具栏
        ]
        
        # 跳过静态文件
        for pattern in skip_patterns:
            if path.startswith(pattern):
                return True
        
        # 跳过文件扩展名
        skip_extensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot', '.map']
        for ext in skip_extensions:
            if path.endswith(ext):
                return True
        
        return False
    
    def _check_api_permission(self, path, method):
        """检查API权限"""
        # 获取权限级别
        permission_level = self._get_api_permission_level(path)
        
        if permission_level == PERMISSION_LEVELS['PUBLIC']:
            # 公开接口，无需验证
            return None
        
        # 检查登录状态
        if not self._is_logged_in():
            logger.warning(f"全局中间件: 未登录访问受保护API - {path}, IP: {request.remote_addr}")
            return jsonify({
                'error': '请先登录',
                'code': 'AUTH_REQUIRED',
                'redirect': '/login'
            }), 401
        
        # 检查管理员权限
        if permission_level == PERMISSION_LEVELS['ADMIN']:
            if not self._is_admin():
                logger.warning(f"全局中间件: 非管理员访问管理员API - {path}, 用户: {session.get('username')}")
                return jsonify({
                    'error': '权限不足，需要管理员权限',
                    'code': 'INSUFFICIENT_PERMISSION'
                }), 403
        
        # 验证会话有效性
        if not self._is_session_valid():
            session.clear()
            logger.warning(f"全局中间件: 会话已过期 - {path}, 用户: {session.get('username')}")
            return jsonify({
                'error': '会话已过期，请重新登录',
                'code': 'SESSION_EXPIRED',
                'redirect': '/login'
            }), 401
        
        # 设置用户上下文
        self._set_user_context()
        
        return None
    
    def _check_page_permission(self, path):
        """检查页面权限"""
        # 获取权限级别
        permission_level = self._get_page_permission_level(path)
        
        if permission_level == PERMISSION_LEVELS['PUBLIC']:
            # 公开页面，无需验证
            return None
        
        # 检查登录状态
        if not self._is_logged_in():
            logger.warning(f"全局中间件: 未登录访问受保护页面 - {path}, IP: {request.remote_addr}")
            # 页面访问重定向到登录页
            from flask import redirect
            return redirect('/login')
        
        # 验证会话有效性
        if not self._is_session_valid():
            session.clear()
            logger.warning(f"全局中间件: 会话已过期访问页面 - {path}, 用户: {session.get('username')}")
            from flask import redirect
            return redirect('/login')
        
        return None
    
    def _get_api_permission_level(self, path):
        """获取API权限级别"""
        # 精确匹配
        if path in API_PERMISSIONS:
            return API_PERMISSIONS[path]
        
        # 前缀匹配
        for api_path, level in API_PERMISSIONS.items():
            if path.startswith(api_path):
                return level
        
        # 默认需要登录
        logger.info(f"全局中间件: API路径未在权限配置中找到，默认需要登录 - {path}")
        return PERMISSION_LEVELS['LOGIN']
    
    def _get_page_permission_level(self, path):
        """获取页面权限级别"""
        # 精确匹配
        if path in PAGE_PERMISSIONS:
            return PAGE_PERMISSIONS[path]
        
        # 前缀匹配
        for page_path, level in PAGE_PERMISSIONS.items():
            if path.startswith(page_path):
                return level
        
        # 默认需要登录
        logger.info(f"全局中间件: 页面路径未在权限配置中找到，默认需要登录 - {path}")
        return PERMISSION_LEVELS['LOGIN']
    
    def _is_logged_in(self):
        """检查是否已登录"""
        return 'user_id' in session and 'username' in session
    
    def _is_admin(self):
        """检查是否是管理员"""
        return session.get('role') == 'admin'
    
    def _is_session_valid(self):
        """检查会话是否有效"""
        try:
            from modules.auth.services.session_manager import session_manager
            return session_manager.validate_session(session.get('session_id'))
        except Exception as e:
            logger.error(f"全局中间件: 会话验证异常 - {str(e)}")
            return False
    
    def _set_user_context(self):
        """设置用户上下文"""
        if hasattr(g, 'current_user'):
            return  # 已经设置过了
        
        g.current_user = {
            'id': session['user_id'],
            'username': session['username'],
            'role': session['role']
        }

# 创建全局实例
global_auth_middleware = GlobalAuthMiddleware()
